-- =====================================================
-- EXECUTE ESTE SQL NO BANCO u880879026_appDash
-- Atualização da tabela client_posts para Product Review
-- =====================================================

-- Adicionar novos campos para Product Review
ALTER TABLE client_posts 
ADD COLUMN product_description LONGTEXT NULL AFTER content,
ADD COLUMN post_cover TEXT NULL AFTER product_description,
ADD COLUMN keyword VARCHAR(255) NULL AFTER post_cover,
ADD COLUMN existing_tags TEXT NULL AFTER tags,
ADD COLUMN youtube_video TEXT NULL AFTER existing_tags,
ADD COLUMN product_images JSON NULL AFTER youtube_video;

-- Verificar estrutura atualizada
DESCRIBE client_posts;

-- Mostrar as novas colunas adicionadas
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'u880879026_appDash' 
AND TABLE_NAME = 'client_posts' 
AND COLUMN_NAME IN ('product_description', 'post_cover', 'keyword', 'existing_tags', 'youtube_video', 'product_images');
