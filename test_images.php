<?php
/**
 * <PERSON>ript para testar e verificar imagens dos posts
 */

require_once __DIR__ . '/bootstrap.php';

try {
    $db = Database::getInstance();
    
    echo "=== TESTE DE IMAGENS DOS POSTS ===\n\n";
    
    // 1. Verificar estrutura da tabela
    echo "1. Verificando estrutura da tabela...\n";
    $columns = $db->fetchAll("DESCRIBE client_posts");
    $fields = array_column($columns, 'Field');
    
    $imageFields = ['product_images', 'post_cover'];
    foreach ($imageFields as $field) {
        if (in_array($field, $fields)) {
            echo "✅ Campo '$field' existe\n";
        } else {
            echo "❌ Campo '$field' NÃO existe\n";
        }
    }
    
    // 2. Verificar posts existentes
    echo "\n2. Verificando posts existentes...\n";
    $posts = $db->fetchAll("SELECT id, title, product_images, post_cover, status FROM client_posts ORDER BY id DESC LIMIT 5");
    
    if (empty($posts)) {
        echo "❌ Nenhum post encontrado\n";
    } else {
        foreach ($posts as $post) {
            echo "Post ID {$post['id']}: {$post['title']}\n";
            echo "  - Status: {$post['status']}\n";
            echo "  - product_images: " . ($post['product_images'] ?? 'NULL') . "\n";
            echo "  - post_cover: " . ($post['post_cover'] ?? 'NULL') . "\n";
            echo "  ---\n";
        }
    }
    
    // 3. Testar inserção de dados de teste
    echo "\n3. Testando inserção de dados de teste...\n";
    
    $testImages = [
        '/app/uploads/posts/test/image1.jpg',
        '/app/uploads/posts/test/image2.jpg',
        '/app/uploads/posts/test/image3.jpg'
    ];
    
    $testCover = '/app/uploads/posts/test/cover.jpg';
    
    // Verificar se existe um post para atualizar
    $testPost = $db->fetch("SELECT id FROM client_posts WHERE status = 'draft' LIMIT 1");
    
    if ($testPost) {
        echo "Atualizando post ID {$testPost['id']} com dados de teste...\n";
        
        $updateResult = $db->update('client_posts', [
            'product_images' => json_encode($testImages),
            'post_cover' => $testCover
        ], 'id = :id', ['id' => $testPost['id']]);
        
        if ($updateResult) {
            echo "✅ Post atualizado com sucesso\n";
            
            // Verificar se foi salvo corretamente
            $updatedPost = $db->fetch("SELECT product_images, post_cover FROM client_posts WHERE id = :id", ['id' => $testPost['id']]);
            echo "Dados salvos:\n";
            echo "  - product_images: " . $updatedPost['product_images'] . "\n";
            echo "  - post_cover: " . $updatedPost['post_cover'] . "\n";
        } else {
            echo "❌ Erro ao atualizar post\n";
        }
    } else {
        echo "ℹ️ Nenhum post em rascunho encontrado para teste\n";
    }
    
    // 4. Testar API
    echo "\n4. Testando API...\n";
    
    // Simular chamada da API
    $apiPosts = $db->fetchAll("
        SELECT 
            cp.id,
            cp.title,
            cp.product_images,
            cp.post_cover,
            cs.site_name,
            cs.site_url
        FROM client_posts cp
        INNER JOIN client_sites cs ON cp.site_id = cs.id
        WHERE cp.status = 'pending'
        LIMIT 1
    ");
    
    if (!empty($apiPosts)) {
        $post = $apiPosts[0];
        echo "Post encontrado para API:\n";
        echo "  - ID: {$post['id']}\n";
        echo "  - Título: {$post['title']}\n";
        echo "  - product_images (raw): " . ($post['product_images'] ?? 'NULL') . "\n";
        echo "  - post_cover (raw): " . ($post['post_cover'] ?? 'NULL') . "\n";
        
        // Processar como a API faria
        if (!empty($post['product_images'])) {
            $images = json_decode($post['product_images'], true);
            if (is_array($images)) {
                $baseUrl = 'https://app.melhorcupom.shop';
                $processedImages = array_map(function($url) use ($baseUrl) {
                    return $baseUrl . $url;
                }, $images);
                echo "  - product_images (processadas): " . json_encode($processedImages) . "\n";
            }
        }
        
        if (!empty($post['post_cover'])) {
            $baseUrl = 'https://app.melhorcupom.shop';
            $processedCover = $baseUrl . $post['post_cover'];
            echo "  - post_cover (processada): $processedCover\n";
        }
    } else {
        echo "ℹ️ Nenhum post pendente encontrado\n";
    }
    
    echo "\n=== TESTE CONCLUÍDO ===\n";
    
} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage() . "\n";
}
?>
