<?php
/**
 * Página temporária para corrigir estrutura do banco
 */

// Verificar se é uma requisição POST para executar o SQL
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['execute'])) {
    require_once __DIR__ . '/bootstrap.php';
    
    try {
        $db = Database::getInstance();
        
        // Verificar estrutura atual
        $columns = $db->fetchAll("DESCRIBE client_posts");
        $existingFields = array_column($columns, 'Field');
        
        $results = [];
        
        // Verificar e adicionar campos necessários
        $fieldsToAdd = [
            'product_images' => 'TEXT NULL',
            'post_cover' => 'TEXT NULL'
        ];

        foreach ($fieldsToAdd as $field => $definition) {
            if (!in_array($field, $existingFields)) {
                try {
                    $sql = "ALTER TABLE client_posts ADD COLUMN $field $definition";
                    $db->query($sql);
                    $results[] = "✅ Campo '$field' adicionado com sucesso";
                } catch (Exception $e) {
                    $results[] = "❌ Erro ao adicionar campo '$field': " . $e->getMessage();
                }
            } else {
                $results[] = "ℹ️ Campo '$field' já existe";
            }
        }

        // Testar inserção de dados de exemplo
        if (isset($_POST['add_test_data'])) {
            $testPost = $db->fetch("SELECT id FROM client_posts LIMIT 1");
            if ($testPost) {
                $testImages = json_encode([
                    '/app/uploads/posts/test/image1.jpg',
                    '/app/uploads/posts/test/image2.jpg',
                    '/app/uploads/posts/test/image3.jpg'
                ]);
                $testCover = '/app/uploads/posts/test/cover.jpg';

                try {
                    $db->update('client_posts', [
                        'product_images' => $testImages,
                        'post_cover' => $testCover
                    ], 'id = :id', ['id' => $testPost['id']]);

                    $results[] = "✅ Dados de teste adicionados ao post ID {$testPost['id']}";
                } catch (Exception $e) {
                    $results[] = "❌ Erro ao adicionar dados de teste: " . $e->getMessage();
                }
            } else {
                $results[] = "❌ Nenhum post encontrado para adicionar dados de teste";
            }
        }

        // Corrigir post ID 1 com imagens reais
        if (isset($_POST['fix_post_1'])) {
            $post1 = $db->fetch("SELECT id FROM client_posts WHERE id = 1");
            if ($post1) {
                // URLs das imagens reais que existem no servidor
                $realImages = json_encode([
                    '/app/uploads/posts/1/image_1_1750609221.png',
                    '/app/uploads/posts/1/image_2_1750609221.png',
                    '/app/uploads/posts/1/image_3_1750609221.png',
                    '/app/uploads/posts/1/image_4_1750609221.png',
                    '/app/uploads/posts/1/image_5_1750609221.png'
                ]);
                $realCover = '/app/uploads/posts/1/cover_1750609221.png';

                try {
                    $db->update('client_posts', [
                        'product_images' => $realImages,
                        'post_cover' => $realCover
                    ], 'id = :id', ['id' => 1]);

                    $results[] = "✅ Post ID 1 corrigido com imagens reais!";

                    // Verificar se foi salvo
                    $updated = $db->fetch("SELECT product_images, post_cover FROM client_posts WHERE id = 1");
                    $results[] = "📋 Dados salvos no post ID 1:";
                    $results[] = "  - product_images: " . $updated['product_images'];
                    $results[] = "  - post_cover: " . $updated['post_cover'];

                } catch (Exception $e) {
                    $results[] = "❌ Erro ao corrigir post ID 1: " . $e->getMessage();
                }
            } else {
                $results[] = "❌ Post ID 1 não encontrado";
            }
        }
        
        // Verificar estrutura final
        $finalColumns = $db->fetchAll("DESCRIBE client_posts");
        $finalFields = array_column($finalColumns, 'Field');
        
        $imageFieldsExist = true;
        foreach (['product_images', 'post_cover'] as $field) {
            if (!in_array($field, $finalFields)) {
                $imageFieldsExist = false;
                break;
            }
        }
        
        if ($imageFieldsExist) {
            $results[] = "🎉 Todos os campos de imagem estão presentes!";

            // Mostrar dados atuais dos posts
            $posts = $db->fetchAll("SELECT id, title, product_images, post_cover FROM client_posts ORDER BY id DESC LIMIT 3");
            if (!empty($posts)) {
                $results[] = "📋 Posts atuais:";
                foreach ($posts as $post) {
                    $title = substr($post['title'], 0, 50) . '...';
                    $images = $post['product_images'] ? 'SIM' : 'NÃO';
                    $cover = $post['post_cover'] ? 'SIM' : 'NÃO';
                    $results[] = "  - ID {$post['id']}: $title | Imagens: $images | Capa: $cover";
                }
            }
        }
        
    } catch (Exception $e) {
        $results = ["❌ Erro: " . $e->getMessage()];
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Correção do Banco de Dados</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .result { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Correção da Estrutura do Banco de Dados</h1>
        
        <p>Esta página irá verificar e corrigir a estrutura da tabela <code>client_posts</code> para adicionar os campos necessários para upload de imagens.</p>
        
        <?php if (isset($results)): ?>
            <h2>Resultados:</h2>
            <?php foreach ($results as $result): ?>
                <?php 
                $class = 'info';
                if (strpos($result, '✅') !== false || strpos($result, '🎉') !== false) $class = 'success';
                if (strpos($result, '❌') !== false) $class = 'error';
                ?>
                <div class="result <?= $class ?>"><?= htmlspecialchars($result) ?></div>
            <?php endforeach; ?>
        <?php endif; ?>
        
        <form method="POST">
            <button type="submit" name="execute" value="1">Executar Correção</button>
            <button type="submit" name="execute" value="1" name="add_test_data" value="1" style="margin-left: 10px; background: #28a745;">Adicionar Dados de Teste</button>
        </form>

        <form method="POST" style="margin-top: 10px;">
            <button type="submit" name="add_test_data" value="1" style="background: #17a2b8; color: white; border: none; padding: 10px 20px; border-radius: 5px;">Adicionar Dados de Teste</button>
        </form>

        <form method="POST" style="margin-top: 10px;">
            <button type="submit" name="fix_post_1" value="1" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px;">Corrigir Post ID 1 com Imagens Reais</button>
        </form>
        
        <h2>Campos que serão verificados/adicionados:</h2>
        <ul>
            <li><code>product_images</code> - Para armazenar URLs das imagens do produto</li>
            <li><code>post_cover</code> - Para armazenar URL da capa do post</li>
        </ul>

        <h2>Arquivos físicos encontrados:</h2>
        <?php
        $uploadDir = __DIR__ . '/uploads/posts';
        if (is_dir($uploadDir)) {
            $postDirs = glob($uploadDir . '/*', GLOB_ONLYDIR);
            if (!empty($postDirs)) {
                echo "<ul>";
                foreach ($postDirs as $postDir) {
                    $postId = basename($postDir);
                    $files = glob($postDir . '/*');
                    echo "<li><strong>Post ID $postId:</strong> " . count($files) . " arquivos";
                    if (!empty($files)) {
                        echo "<ul>";
                        foreach ($files as $file) {
                            $fileName = basename($file);
                            $size = round(filesize($file) / 1024, 2);
                            echo "<li><code>$fileName</code> ($size KB)</li>";
                        }
                        echo "</ul>";
                    }
                    echo "</li>";
                }
                echo "</ul>";
            } else {
                echo "<p>Nenhum diretório de post encontrado.</p>";
            }
        } else {
            echo "<p>Diretório de uploads não existe.</p>";
        }
        ?>
    </div>
</body>
</html>
